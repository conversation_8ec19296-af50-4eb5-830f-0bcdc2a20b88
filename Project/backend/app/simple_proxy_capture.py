"""
简化版本的代理抓包模块
使用更简单可靠的方法，避免mitmproxy的复杂线程问题
基于HTTP代理的简单实现
"""

import socket
import threading
import logging
import time
import re
import urllib.parse
import subprocess
import platform
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MediaResource:
    """媒体资源数据结构"""
    url: str
    content_type: str
    size: Optional[int] = None
    duration: Optional[float] = None
    title: Optional[str] = None
    timestamp: float = None

class SimpleMediaCollector:
    """简单的媒体资源收集器"""
    
    def __init__(self):
        self.resources: List[MediaResource] = []
        self.video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'}
        self.audio_extensions = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma'}
        self.streaming_patterns = [
            'youtube.com', 'youtu.be', 'bilibili.com', 'douyin.com', 'tiktok.com',
            'netflix.com', 'hulu.com', 'amazon.com', 'twitch.tv', 'vimeo.com'
        ]
    
    def is_media_resource(self, url: str, content_type: str = '') -> bool:
        """判断是否为媒体资源"""
        url_lower = url.lower()
        
        # 检查Content-Type
        if content_type:
            if any(media_type in content_type.lower() for media_type in ['video/', 'audio/', 'application/octet-stream']):
                return True
        
        # 检查URL扩展名
        if any(ext in url_lower for ext in self.video_extensions | self.audio_extensions):
            return True
            
        # 检查流媒体平台
        if any(pattern in url_lower for pattern in self.streaming_patterns):
            return True
            
        # 检查常见的媒体URL模式
        media_patterns = [
            r'/video/', r'/audio/', r'/media/', r'/stream/',
            r'\.m3u8', r'\.ts$', r'\.mpd$', r'\.dash$'
        ]
        
        for pattern in media_patterns:
            if re.search(pattern, url_lower):
                return True
                
        return False
    
    def add_resource(self, url: str, content_type: str = '', size: int = None):
        """添加媒体资源"""
        if self.is_media_resource(url, content_type):
            resource = MediaResource(
                url=url,
                content_type=content_type,
                size=size,
                timestamp=time.time()
            )
            
            # 避免重复
            if not any(r.url == url for r in self.resources):
                self.resources.append(resource)
                logger.info(f"🎬 发现媒体资源: {url[:100]}...")
                return True
        return False

class SystemProxyManager:
    """系统代理管理器"""

    def __init__(self):
        self.platform = platform.system().lower()
        self.original_proxy_settings = None

    def set_system_proxy(self, proxy_host: str, proxy_port: int) -> bool:
        """设置系统代理"""
        try:
            if self.platform == "darwin":  # macOS
                return self._set_macos_proxy(proxy_host, proxy_port)
            elif self.platform == "windows":
                return self._set_windows_proxy(proxy_host, proxy_port)
            elif self.platform == "linux":
                return self._set_linux_proxy(proxy_host, proxy_port)
            else:
                logger.warning(f"不支持的操作系统: {self.platform}")
                return False
        except Exception as e:
            logger.error(f"设置系统代理失败: {e}")
            return False

    def _set_macos_proxy(self, proxy_host: str, proxy_port: int) -> bool:
        """设置macOS系统代理"""
        try:
            # 获取当前网络服务
            result = subprocess.run(
                ["networksetup", "-listallnetworkservices"],
                capture_output=True, text=True, check=True
            )

            services = []
            for line in result.stdout.split('\n'):
                line = line.strip()
                if line and not line.startswith('*') and 'An asterisk' not in line:
                    services.append(line)

            # 为每个网络服务设置代理
            for service in services:
                if service:
                    # 设置HTTP代理
                    subprocess.run([
                        "networksetup", "-setwebproxy", service, proxy_host, str(proxy_port)
                    ], check=True)

                    # 设置HTTPS代理
                    subprocess.run([
                        "networksetup", "-setsecurewebproxy", service, proxy_host, str(proxy_port)
                    ], check=True)

                    logger.info(f"✅ 已为 {service} 设置代理: {proxy_host}:{proxy_port}")

            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"设置macOS代理失败: {e}")
            return False

    def _set_windows_proxy(self, proxy_host: str, proxy_port: int) -> bool:
        """设置Windows系统代理"""
        try:
            proxy_server = f"{proxy_host}:{proxy_port}"

            # 使用注册表设置代理
            import winreg

            # 打开注册表项
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                0, winreg.KEY_ALL_ACCESS
            )

            # 启用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)

            # 设置代理服务器
            winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, proxy_server)

            winreg.CloseKey(key)

            logger.info(f"✅ 已设置Windows代理: {proxy_server}")
            return True

        except Exception as e:
            logger.error(f"设置Windows代理失败: {e}")
            return False

    def _set_linux_proxy(self, proxy_host: str, proxy_port: int) -> bool:
        """设置Linux系统代理"""
        try:
            proxy_url = f"http://{proxy_host}:{proxy_port}"

            # 设置环境变量
            os.environ['http_proxy'] = proxy_url
            os.environ['https_proxy'] = proxy_url
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url

            logger.info(f"✅ 已设置Linux代理环境变量: {proxy_url}")
            return True

        except Exception as e:
            logger.error(f"设置Linux代理失败: {e}")
            return False

    def clear_system_proxy(self) -> bool:
        """清除系统代理"""
        try:
            if self.platform == "darwin":  # macOS
                return self._clear_macos_proxy()
            elif self.platform == "windows":
                return self._clear_windows_proxy()
            elif self.platform == "linux":
                return self._clear_linux_proxy()
            else:
                return False
        except Exception as e:
            logger.error(f"清除系统代理失败: {e}")
            return False

    def _clear_macos_proxy(self) -> bool:
        """清除macOS系统代理"""
        try:
            # 获取网络服务列表
            result = subprocess.run(
                ["networksetup", "-listallnetworkservices"],
                capture_output=True, text=True, check=True
            )

            services = []
            for line in result.stdout.split('\n'):
                line = line.strip()
                if line and not line.startswith('*') and 'An asterisk' not in line:
                    services.append(line)

            # 清除每个服务的代理设置
            for service in services:
                if service:
                    # 关闭HTTP代理
                    subprocess.run([
                        "networksetup", "-setwebproxystate", service, "off"
                    ], check=True)

                    # 关闭HTTPS代理
                    subprocess.run([
                        "networksetup", "-setsecurewebproxystate", service, "off"
                    ], check=True)

                    logger.info(f"✅ 已清除 {service} 的代理设置")

            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"清除macOS代理失败: {e}")
            return False

    def _clear_windows_proxy(self) -> bool:
        """清除Windows系统代理"""
        try:
            import winreg

            # 打开注册表项
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                0, winreg.KEY_ALL_ACCESS
            )

            # 禁用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)

            winreg.CloseKey(key)

            logger.info("✅ 已清除Windows代理设置")
            return True

        except Exception as e:
            logger.error(f"清除Windows代理失败: {e}")
            return False

    def _clear_linux_proxy(self) -> bool:
        """清除Linux系统代理"""
        try:
            # 清除环境变量
            for var in ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY']:
                if var in os.environ:
                    del os.environ[var]

            logger.info("✅ 已清除Linux代理环境变量")
            return True

        except Exception as e:
            logger.error(f"清除Linux代理失败: {e}")
            return False

class SimpleHTTPProxy:
    """简单的HTTP代理服务器 - 支持系统级代理设置"""

    def __init__(self, port: int = 8899, upstream_proxy: str = None):
        self.port = port
        self.upstream_proxy = upstream_proxy
        self.collector = SimpleMediaCollector()
        self.running = False
        self.server_socket = None
        self.server_thread = None
        self.system_proxy_manager = SystemProxyManager()
        self.system_proxy_enabled = False
        
    def parse_upstream_proxy(self, upstream_proxy: str) -> tuple:
        """解析上游代理地址"""
        if not upstream_proxy:
            return None, None
            
        # 移除协议前缀
        if upstream_proxy.startswith('http://'):
            upstream_proxy = upstream_proxy[7:]
        elif upstream_proxy.startswith('https://'):
            upstream_proxy = upstream_proxy[8:]
            
        # 解析主机和端口
        if ':' in upstream_proxy:
            host, port = upstream_proxy.split(':', 1)
            return host, int(port)
        else:
            return upstream_proxy, 80
    
    def handle_client(self, client_socket, client_address):
        """处理客户端连接"""
        try:
            # 接收客户端请求
            request = client_socket.recv(4096).decode('utf-8')
            if not request:
                return
                
            # 解析HTTP请求
            lines = request.split('\n')
            if not lines:
                return
                
            # 解析请求行
            request_line = lines[0].strip()
            if not request_line:
                return
                
            method, url, version = request_line.split(' ', 2)
            
            # 记录媒体资源
            self.collector.add_resource(url)
            
            # 处理CONNECT方法（HTTPS）
            if method == 'CONNECT':
                self.handle_connect(client_socket, url)
                return
            
            # 处理HTTP请求
            self.handle_http_request(client_socket, request, url)
            
        except Exception as e:
            logger.error(f"处理客户端连接时出错: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass
    
    def handle_connect(self, client_socket, url):
        """处理HTTPS CONNECT请求"""
        try:
            # 解析目标地址
            host, port = url.split(':')
            port = int(port)
            
            # 如果有上游代理，连接到上游代理
            if self.upstream_proxy:
                upstream_host, upstream_port = self.parse_upstream_proxy(self.upstream_proxy)
                target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                target_socket.connect((upstream_host, upstream_port))
                
                # 向上游代理发送CONNECT请求
                connect_request = f"CONNECT {url} HTTP/1.1\r\nHost: {url}\r\n\r\n"
                target_socket.send(connect_request.encode())
                
                # 接收上游代理的响应
                response = target_socket.recv(4096)
                client_socket.send(response)
            else:
                # 直接连接到目标服务器
                target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                target_socket.connect((host, port))
                
                # 发送连接成功响应
                client_socket.send(b"HTTP/1.1 200 Connection established\r\n\r\n")
            
            # 开始数据转发
            self.forward_data(client_socket, target_socket)
            
        except Exception as e:
            logger.error(f"处理CONNECT请求时出错: {e}")
            try:
                client_socket.send(b"HTTP/1.1 500 Internal Server Error\r\n\r\n")
            except:
                pass
    
    def handle_http_request(self, client_socket, request, url):
        """处理HTTP请求"""
        try:
            # 解析URL
            parsed_url = urllib.parse.urlparse(url)
            host = parsed_url.hostname
            port = parsed_url.port or 80
            
            # 如果有上游代理，连接到上游代理
            if self.upstream_proxy:
                upstream_host, upstream_port = self.parse_upstream_proxy(self.upstream_proxy)
                target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                target_socket.connect((upstream_host, upstream_port))
                
                # 向上游代理发送原始请求
                target_socket.send(request.encode())
            else:
                # 直接连接到目标服务器
                target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                target_socket.connect((host, port))
                
                # 修改请求为相对路径
                modified_request = request.replace(url, parsed_url.path or '/')
                target_socket.send(modified_request.encode())
            
            # 接收响应并转发
            response = target_socket.recv(4096)
            client_socket.send(response)
            
            # 继续转发数据
            self.forward_data(client_socket, target_socket)
            
        except Exception as e:
            logger.error(f"处理HTTP请求时出错: {e}")
    
    def forward_data(self, client_socket, target_socket):
        """双向数据转发"""
        def forward(source, destination):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
            except:
                pass
            finally:
                try:
                    source.close()
                    destination.close()
                except:
                    pass
        
        # 创建两个线程进行双向转发
        client_to_target = threading.Thread(target=forward, args=(client_socket, target_socket))
        target_to_client = threading.Thread(target=forward, args=(target_socket, client_socket))
        
        client_to_target.daemon = True
        target_to_client.daemon = True
        
        client_to_target.start()
        target_to_client.start()
        
        # 等待其中一个方向结束
        client_to_target.join(timeout=1)
        target_to_client.join(timeout=1)
    
    def start(self, auto_set_system_proxy: bool = True) -> Dict[str, Any]:
        """启动代理服务"""
        try:
            if self.running:
                return {
                    'success': True,
                    'message': '代理服务已在运行',
                    'port': self.port,
                    'system_proxy_enabled': self.system_proxy_enabled
                }

            logger.info(f"🚀 启动简单HTTP代理服务，端口: {self.port}")
            if self.upstream_proxy:
                logger.info(f"🔗 使用上游代理: {self.upstream_proxy}")

            # 创建服务器套接字
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('127.0.0.1', self.port))
            self.server_socket.listen(5)

            self.running = True

            # 在新线程中运行服务器
            def run_server():
                logger.info(f"✅ 代理服务器开始监听端口 {self.port}")
                while self.running:
                    try:
                        client_socket, client_address = self.server_socket.accept()
                        # 为每个客户端创建新线程
                        client_thread = threading.Thread(
                            target=self.handle_client,
                            args=(client_socket, client_address)
                        )
                        client_thread.daemon = True
                        client_thread.start()
                    except Exception as e:
                        if self.running:  # 只有在运行时才记录错误
                            logger.error(f"接受连接时出错: {e}")
                        break

                logger.info("🔄 代理服务器已停止")

            self.server_thread = threading.Thread(target=run_server)
            self.server_thread.daemon = True
            self.server_thread.start()

            # 等待一下确保启动成功
            time.sleep(1)

            # 自动设置系统代理
            if auto_set_system_proxy:
                logger.info("🔧 正在设置系统代理...")
                if self.system_proxy_manager.set_system_proxy('127.0.0.1', self.port):
                    self.system_proxy_enabled = True
                    logger.info("✅ 系统代理设置成功 - 现在可以抓取所有应用的网络流量！")
                else:
                    logger.warning("⚠️ 系统代理设置失败，但代理服务仍可手动使用")

            return {
                'success': True,
                'message': '简单代理服务启动成功' + ('，系统代理已自动设置' if self.system_proxy_enabled else ''),
                'port': self.port,
                'upstream_proxy': self.upstream_proxy,
                'system_proxy_enabled': self.system_proxy_enabled,
                'usage_tip': '现在打开任何应用（浏览器、微信等）播放音视频，VideoSense会自动抓取！' if self.system_proxy_enabled else '请手动设置代理为 127.0.0.1:8899'
            }

        except Exception as e:
            logger.error(f"❌ 启动代理服务失败: {e}")
            self.running = False
            return {
                'success': False,
                'error': f'启动失败: {str(e)}'
            }
    
    def stop(self) -> Dict[str, Any]:
        """停止代理服务"""
        try:
            logger.info("🛑 停止简单代理服务...")

            # 清除系统代理设置
            if self.system_proxy_enabled:
                logger.info("🔧 正在清除系统代理设置...")
                if self.system_proxy_manager.clear_system_proxy():
                    self.system_proxy_enabled = False
                    logger.info("✅ 系统代理设置已清除")
                else:
                    logger.warning("⚠️ 清除系统代理设置失败")

            self.running = False

            if self.server_socket:
                self.server_socket.close()

            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=3)

            logger.info("✅ 简单代理服务已停止")
            return {
                'success': True,
                'message': '简单代理服务已停止，系统代理设置已恢复'
            }

        except Exception as e:
            logger.error(f"❌ 停止代理服务失败: {e}")
            return {
                'success': False,
                'error': f'停止失败: {str(e)}'
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取代理状态"""
        return {
            'running': self.running,
            'port': self.port,
            'upstream_proxy': self.upstream_proxy,
            'resources_count': len(self.collector.resources)
        }
    
    def get_resources(self) -> List[Dict[str, Any]]:
        """获取捕获的媒体资源"""
        return [
            {
                'url': resource.url,
                'content_type': resource.content_type,
                'size': resource.size,
                'timestamp': resource.timestamp,
                'title': resource.title
            }
            for resource in self.collector.resources
        ]
    
    def clear_resources(self):
        """清空资源列表"""
        self.collector.resources.clear()
        logger.info("🧹 已清空媒体资源列表")

# 全局实例
_simple_proxy_instance: Optional[SimpleHTTPProxy] = None

def get_simple_proxy_instance(port: int = 8899) -> SimpleHTTPProxy:
    """获取简单代理实例（单例模式）"""
    global _simple_proxy_instance
    if _simple_proxy_instance is None:
        _simple_proxy_instance = SimpleHTTPProxy(port)
    return _simple_proxy_instance

def test_simple_proxy():
    """测试简单代理"""
    logger.info("🧪 测试简单HTTP代理...")
    
    proxy = get_simple_proxy_instance()
    
    # 启动代理
    result = proxy.start()
    logger.info(f"启动结果: {result}")
    
    if result['success']:
        logger.info("✅ 代理启动成功，等待5秒...")
        time.sleep(5)
        
        # 检查状态
        status = proxy.get_status()
        logger.info(f"代理状态: {status}")
        
        # 停止代理
        stop_result = proxy.stop()
        logger.info(f"停止结果: {stop_result}")
    
    return result

if __name__ == "__main__":
    test_simple_proxy()

"""
修复版本的代理抓包模块
彻底解决mitmproxy线程和事件循环问题
"""

import asyncio
import threading
import logging
import time
import signal
import sys
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入mitmproxy
try:
    from mitmproxy import options, master
    from mitmproxy.tools.dump import DumpMaster
    from mitmproxy import http
    from mitmproxy.addons import core
    MITMPROXY_AVAILABLE = True
    logger.info("✅ mitmproxy导入成功")
except ImportError as e:
    MITMPROXY_AVAILABLE = False
    logger.warning(f"⚠️ mitmproxy不可用: {e}")

@dataclass
class MediaResource:
    """媒体资源数据结构"""
    url: str
    content_type: str
    size: Optional[int] = None
    duration: Optional[float] = None
    title: Optional[str] = None
    timestamp: float = None

class MediaResourceCollector:
    """媒体资源收集器"""
    
    def __init__(self):
        self.resources: List[MediaResource] = []
        self.video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'}
        self.audio_extensions = {'.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma'}
        self.streaming_patterns = ['youtube.com', 'bilibili.com', 'douyin.com', 'tiktok.com']
    
    def is_media_resource(self, url: str, content_type: str) -> bool:
        """判断是否为媒体资源"""
        # 检查Content-Type
        if content_type:
            if any(media_type in content_type.lower() for media_type in ['video/', 'audio/']):
                return True
        
        # 检查URL扩展名
        url_lower = url.lower()
        if any(ext in url_lower for ext in self.video_extensions | self.audio_extensions):
            return True
            
        # 检查流媒体平台
        if any(pattern in url_lower for pattern in self.streaming_patterns):
            return True
            
        return False
    
    def process_request(self, flow: 'http.HTTPFlow'):
        """处理HTTP请求/响应"""
        try:
            url = flow.request.pretty_url
            content_type = flow.response.headers.get('content-type', '') if flow.response else ''
            
            if self.is_media_resource(url, content_type):
                resource = MediaResource(
                    url=url,
                    content_type=content_type,
                    size=len(flow.response.content) if flow.response else None,
                    timestamp=time.time()
                )
                
                self.resources.append(resource)
                logger.info(f"🎬 发现媒体资源: {url[:100]}...")
                
        except Exception as e:
            logger.error(f"处理请求时出错: {e}")

class FixedProxyCapture:
    """修复版本的代理抓包服务"""
    
    def __init__(self, port: int = 8899):
        self.port = port
        self.master: Optional[DumpMaster] = None
        self.collector = MediaResourceCollector()
        self.is_running = False
        self.proxy_thread: Optional[threading.Thread] = None
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.upstream_proxy: Optional[str] = None
        
    def _create_addon(self):
        """创建mitmproxy插件"""
        class MediaCaptureAddon:
            def __init__(self, collector):
                self.collector = collector
                
            def request(self, flow: http.HTTPFlow):
                """处理请求"""
                pass
                
            def response(self, flow: http.HTTPFlow):
                """处理响应"""
                self.collector.process_request(flow)
        
        return MediaCaptureAddon(self.collector)
    
    def _run_proxy_in_thread(self, use_upstream: bool = False, upstream_proxy: str = None):
        """在独立线程中运行代理服务"""
        try:
            logger.info("🚀 在新线程中启动mitmproxy...")
            
            # 为这个线程创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            # 配置mitmproxy选项
            opts = options.Options(
                listen_port=self.port,
                listen_host='127.0.0.1',
                ssl_insecure=True,
                confdir='~/.mitmproxy'  # 指定配置目录
            )
            
            # 配置上游代理
            if use_upstream and upstream_proxy:
                opts.mode = f"upstream:{upstream_proxy}"
                logger.info(f"🔗 配置上游代理: {upstream_proxy}")
            else:
                opts.mode = "regular"
                logger.info("📡 使用常规代理模式")
            
            # 创建master
            self.master = DumpMaster(opts, with_termlog=False, with_dumper=False)
            
            # 添加我们的插件
            addon = self._create_addon()
            self.master.addons.add(addon)
            
            logger.info(f"✅ mitmproxy master创建成功，监听端口: {self.port}")
            
            # 标记为运行状态
            self.is_running = True
            
            # 运行事件循环
            self.loop.run_until_complete(self.master.run())
            
        except Exception as e:
            logger.error(f"❌ 代理服务运行失败: {e}")
            self.is_running = False
        finally:
            logger.info("🔄 代理线程结束")
            if self.loop and not self.loop.is_closed():
                self.loop.close()
    
    def start(self, auto_setup_system_proxy: bool = False, smart_mode: bool = True, upstream_proxy: str = None) -> Dict[str, Any]:
        """启动代理服务"""
        if not MITMPROXY_AVAILABLE:
            return {
                'success': False,
                'error': 'mitmproxy不可用，请安装mitmproxy'
            }
        
        if self.is_running:
            return {
                'success': True,
                'message': '代理服务已在运行',
                'port': self.port
            }
        
        try:
            logger.info(f"🎯 启动VideoSense代理服务，端口: {self.port}")
            
            # 保存上游代理配置
            self.upstream_proxy = upstream_proxy
            use_upstream = bool(upstream_proxy)
            
            # 在新线程中启动代理
            self.proxy_thread = threading.Thread(
                target=self._run_proxy_in_thread,
                args=(use_upstream, upstream_proxy),
                daemon=True
            )
            self.proxy_thread.start()
            
            # 等待启动
            max_wait = 10  # 最多等待10秒
            wait_count = 0
            while not self.is_running and wait_count < max_wait:
                time.sleep(1)
                wait_count += 1
            
            if self.is_running:
                logger.info("✅ 代理服务启动成功")
                return {
                    'success': True,
                    'message': '代理服务启动成功',
                    'port': self.port,
                    'upstream_proxy': upstream_proxy,
                    'smart_mode': smart_mode
                }
            else:
                logger.error("❌ 代理服务启动超时")
                return {
                    'success': False,
                    'error': '代理服务启动超时'
                }
                
        except Exception as e:
            logger.error(f"❌ 启动代理服务失败: {e}")
            return {
                'success': False,
                'error': f'启动失败: {str(e)}'
            }
    
    def stop(self) -> Dict[str, Any]:
        """停止代理服务"""
        try:
            logger.info("🛑 停止代理服务...")
            
            if self.master and self.loop:
                # 在事件循环中停止master
                if not self.loop.is_closed():
                    asyncio.run_coroutine_threadsafe(self.master.shutdown(), self.loop)
                
            self.is_running = False
            
            # 等待线程结束
            if self.proxy_thread and self.proxy_thread.is_alive():
                self.proxy_thread.join(timeout=5)
            
            logger.info("✅ 代理服务已停止")
            return {
                'success': True,
                'message': '代理服务已停止'
            }
            
        except Exception as e:
            logger.error(f"❌ 停止代理服务失败: {e}")
            return {
                'success': False,
                'error': f'停止失败: {str(e)}'
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取代理状态"""
        return {
            'running': self.is_running,
            'port': self.port,
            'upstream_proxy': self.upstream_proxy,
            'resources_count': len(self.collector.resources)
        }
    
    def get_resources(self) -> List[Dict[str, Any]]:
        """获取捕获的媒体资源"""
        return [
            {
                'url': resource.url,
                'content_type': resource.content_type,
                'size': resource.size,
                'timestamp': resource.timestamp,
                'title': resource.title
            }
            for resource in self.collector.resources
        ]
    
    def clear_resources(self):
        """清空资源列表"""
        self.collector.resources.clear()
        logger.info("🧹 已清空媒体资源列表")

# 全局实例
_proxy_instance: Optional[FixedProxyCapture] = None

def get_proxy_instance(port: int = 8899) -> FixedProxyCapture:
    """获取代理实例（单例模式）"""
    global _proxy_instance
    if _proxy_instance is None:
        _proxy_instance = FixedProxyCapture(port)
    return _proxy_instance

def test_fixed_proxy():
    """测试修复版本的代理"""
    logger.info("🧪 测试修复版本的代理抓包...")
    
    proxy = get_proxy_instance()
    
    # 启动代理
    result = proxy.start()
    logger.info(f"启动结果: {result}")
    
    if result['success']:
        logger.info("✅ 代理启动成功，等待5秒...")
        time.sleep(5)
        
        # 检查状态
        status = proxy.get_status()
        logger.info(f"代理状态: {status}")
        
        # 停止代理
        stop_result = proxy.stop()
        logger.info(f"停止结果: {stop_result}")
    
    return result

if __name__ == "__main__":
    test_fixed_proxy()

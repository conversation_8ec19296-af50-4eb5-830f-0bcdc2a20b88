"""
代理抓包API路由
提供代理服务控制和资源管理的REST API接口
参考 res-downloader 的功能设计
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from typing import Dict, List, Any, Optional
import logging
from pydantic import BaseModel
from ..proxy_capture import get_proxy_capture
import asyncio
import json

# 检查代理功能是否可用
try:
    from ..proxy_capture import MITMPROXY_AVAILABLE
    PROXY_AVAILABLE = MITMPROXY_AVAILABLE
except ImportError:
    PROXY_AVAILABLE = False

# 添加增强代理检测的导入
from ..enhanced_proxy_solution import EnhancedProxyManager

# VideoSense 系统代理管理 API 路由
from ..core.system_proxy_manager import SystemProxyManager

# 导入修复版本的代理
from ..fixed_proxy_capture import get_proxy_instance as get_fixed_proxy_instance

# 导入简单版本的代理
from ..simple_proxy_capture import get_simple_proxy_instance

router = APIRouter(prefix="/proxy", tags=["代理抓包", "代理管理"])

# 设置日志
logger = logging.getLogger(__name__)

# 全局代理管理器实例
proxy_manager = SystemProxyManager()

# 请求模型
class DownloadRequest(BaseModel):
    url: str
    title: Optional[str] = None
    type: Optional[str] = None

class ProxyTestRequest(BaseModel):
    url: str

class ProxyConfig(BaseModel):
    """代理配置模型"""
    host: str = "127.0.0.1"
    port: int = 8899
    proxy_type: str = "http"

@router.get("/status")
async def get_proxy_status() -> Dict[str, Any]:
    """
    获取代理服务状态
    返回运行状态、端口、资源数量等信息
    """
    if not PROXY_AVAILABLE:
        return {
            'success': False,
            'running': False,
            'available': False,
            'error': '代理抓包功能不可用，请检查 mitmproxy 依赖'
        }
    
    try:
        proxy = get_proxy_capture()
        status = proxy.get_status()
        
        # 添加系统代理状态检查
        system_proxy_status = proxy.check_system_proxy_status()
        status.update({
            'system_proxy': system_proxy_status
        })
        
        return status
    except Exception as e:
        logging.error(f"获取代理状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.post("/start")
async def start_proxy(auto_setup_proxy: bool = True, smart_mode: bool = True) -> Dict[str, Any]:
    """
    启动代理抓包服务
    在指定端口启动mitmproxy代理服务器
    支持智能模式，自动检测现有代理并使用代理链避免冲突
    """
    if not PROXY_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="代理抓包功能不可用，请安装 mitmproxy 依赖"
        )
    
    try:
        proxy = get_proxy_capture()
        result = proxy.start(auto_setup_system_proxy=auto_setup_proxy, smart_mode=smart_mode)
        
        if not result.get('success'):
            raise HTTPException(
                status_code=500, 
                detail=result.get('error', '启动代理服务失败')
            )
        
        # 添加智能模式信息到响应中
        if smart_mode and result.get('proxy_mode') == 'upstream':
            logging.info(f"🔗 智能模式已启用: {result.get('proxy_mode_info')}")
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"启动代理服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")

@router.post("/stop")
async def stop_proxy(auto_restore_proxy: bool = True) -> Dict[str, Any]:
    """
    停止代理抓包服务
    关闭正在运行的代理服务器
    """
    if not PROXY_AVAILABLE:
        return {
            'success': True,
            'message': '代理服务未运行'
        }
    
    try:
        proxy = get_proxy_capture()
        result = proxy.stop(auto_restore_system_proxy=auto_restore_proxy)
        
        if not result.get('success'):
            raise HTTPException(
                status_code=500, 
                detail=result.get('error', '停止代理服务失败')
            )
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"停止代理服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")

@router.post("/restart")
async def restart_proxy(auto_setup_proxy: bool = True) -> Dict[str, Any]:
    """
    重启代理抓包服务
    先停止（自动恢复代理），再启动（自动设置代理）
    """
    if not PROXY_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="代理抓包功能不可用，请安装 mitmproxy 依赖"
        )
    
    try:
        proxy = get_proxy_capture()
        
        # 先停止（自动恢复代理）
        stop_result = proxy.stop(auto_restore_system_proxy=True)
        if not stop_result.get('success', False):
            return stop_result
        
        # 等待一下确保完全停止
        import time
        time.sleep(1)
        
        # 再启动（自动设置代理）
        start_result = proxy.start(auto_setup_system_proxy=auto_setup_proxy)
        
        if start_result.get('success', False):
            return {
                'success': True,
                'message': '代理服务已重启',
                'details': {
                    'stop': stop_result,
                    'start': start_result
                }
            }
        else:
            return start_result
            
    except Exception as e:
        logging.error(f"重启代理服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启失败: {str(e)}")

@router.get("/resources")
async def get_detected_resources() -> Dict[str, Any]:
    """
    获取检测到的媒体资源列表
    返回所有已检测到的音视频资源信息
    """
    if not PROXY_AVAILABLE:
        return {
            'success': True,
            'resources': [],
            'total': 0,
            'message': '代理抓包功能不可用'
        }
    
    try:
        proxy = get_proxy_capture()
        return proxy.get_resources()
    except Exception as e:
        logging.error(f"获取资源列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取资源失败: {str(e)}")

@router.post("/resources/clear")
async def clear_detected_resources() -> Dict[str, Any]:
    """
    清空检测到的资源列表
    删除所有已存储的媒体资源记录
    """
    if not PROXY_AVAILABLE:
        return {
            'success': True,
            'message': '无可清空的资源'
        }
    
    try:
        proxy = get_proxy_capture()
        return proxy.clear_resources()
    except Exception as e:
        logging.error(f"清空资源列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空失败: {str(e)}")

@router.post("/download")
async def download_resource(
    request: DownloadRequest, 
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    下载指定的媒体资源
    支持直接下载或添加到下载队列
    """
    try:
        # TODO: 实现下载功能
        # 这里可以集成下载器，如 yt-dlp, you-get 等
        logging.info(f"下载请求: {request.title} - {request.url}")
        
        # 模拟下载过程
        def download_file():
            # 这里应该实现真正的下载逻辑
            logging.info(f"开始下载: {request.url}")
            # 可以使用 requests, aiohttp 或专用下载器
            pass
        
        background_tasks.add_task(download_file)
        
        return {
            'success': True,
            'message': f'已开始下载: {request.title or "未知文件"}',
            'url': request.url
        }
    except Exception as e:
        logging.error(f"下载资源失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")

@router.post("/test/connection")
async def test_proxy_connection() -> Dict[str, Any]:
    """
    测试代理连接状态
    检查代理服务是否可以正常工作
    """
    try:
        proxy = get_proxy_capture()
        status = proxy.get_status()
        
        if not status.get('available', False):
            return {
                'success': False,
                'error': 'mitmproxy不可用'
            }
        
        if not status.get('running', False):
            return {
                'success': False,
                'error': '代理服务未运行',
                'suggestion': '请先启动代理服务'
            }
        
        # 检查系统代理设置
        system_status = proxy.check_system_proxy_status()
        
        return {
            'success': True,
            'message': '代理连接正常',
            'proxy_running': True,
            'port': proxy.port,
            'system_proxy': system_status
        }
        
    except Exception as e:
        logging.error(f"测试代理连接失败: {e}")
        return {
            'success': False,
            'error': f'连接测试失败: {str(e)}'
        }

@router.post("/test/simulate")
async def simulate_detected_resources() -> Dict[str, Any]:
    """
    模拟检测到媒体资源（用于测试）
    生成一些测试用的媒体资源数据
    """
    if not PROXY_AVAILABLE:
        return {
            'success': False,
            'error': '代理抓包功能不可用'
        }
    
    try:
        proxy = get_proxy_capture()
        return proxy.simulate_resources()
    except Exception as e:
        logging.error(f"模拟资源失败: {e}")
        raise HTTPException(status_code=500, detail=f"模拟失败: {str(e)}")

@router.get("/info")
async def get_proxy_info() -> Dict[str, Any]:
    """
    获取代理抓包功能信息
    返回功能可用性、版本等信息
    """
    info = {
        'success': True,
        'available': PROXY_AVAILABLE,
        'mitmproxy_available': MITMPROXY_AVAILABLE if PROXY_AVAILABLE else False,
        'features': [
            '网络流量监听',
            '媒体资源自动检测', 
            '支持多种视频格式',
            '批量资源管理',
            '下载功能集成'
        ]
    }
    
    if PROXY_AVAILABLE:
        try:
            proxy = get_proxy_capture()
            status = proxy.get_status()
            info.update({
                'default_port': proxy.port,
                'current_status': status.get('status', 'unknown'),
                'resources_count': status.get('resources_count', 0)
            })
        except Exception as e:
            info['error'] = f'获取状态失败: {str(e)}'
    else:
        info['error'] = '代理抓包功能不可用，请安装 mitmproxy 依赖'
    
    return info 

# === 新增的自动化功能API（参考res-downloader） ===

@router.get("/certificate")
async def get_certificate() -> Dict[str, Any]:
    """
    获取mitmproxy根证书内容
    供客户端安装使用
    """
    try:
        proxy = get_proxy_capture()
        
        # 从proxy_capture模块获取证书内容
        from ..proxy_capture import VIDEOSENSE_CERT_PEM
        
        return {
            'success': True,
            'certificate': VIDEOSENSE_CERT_PEM,
            'format': 'PEM',
            'filename': 'videosense-proxy-ca.crt',
            'message': '证书内容获取成功'
        }
    except Exception as e:
        logging.error(f"获取证书失败: {e}")
        return {
            'success': False,
            'error': f'获取证书失败: {str(e)}'
        }

@router.post("/setup/certificate")
async def install_certificate() -> Dict[str, Any]:
    """安装证书 - 一次性设置，类似res-downloader的安装过程"""
    try:
        proxy = get_proxy_capture()
        return proxy.install_certificate()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/system/proxy-status")
async def get_system_proxy_status() -> Dict[str, Any]:
    """获取系统代理设置状态"""
    try:
        proxy = get_proxy_capture()
        return proxy.check_system_proxy_status()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/system/detect-existing-proxy")
async def detect_existing_proxy() -> Dict[str, Any]:
    """检测现有代理配置"""
    try:
        proxy = get_proxy_capture()
        existing_proxy = proxy.system_proxy_manager.detect_existing_proxy()
        
        if existing_proxy:
            return {
                'success': True,
                'existing_proxy_found': True,
                'proxy_info': existing_proxy,
                'recommendation': {
                    'use_smart_mode': True,
                    'message': f'检测到 {existing_proxy["tool"]}，建议启用智能模式避免冲突'
                }
            }
        else:
            return {
                'success': True,
                'existing_proxy_found': False,
                'proxy_info': None,
                'recommendation': {
                    'use_smart_mode': False,
                    'message': '未检测到现有代理，可使用常规模式'
                }
            }
    except Exception as e:
        logging.error(f"检测现有代理失败: {e}")
        return {
            'success': False,
            'error': f'检测失败: {str(e)}',
            'existing_proxy_found': False
        }

@router.post("/system/proxy/set")
async def set_system_proxy() -> Dict[str, Any]:
    """手动设置系统代理"""
    try:
        proxy = get_proxy_capture()
        result = proxy.system_proxy_manager.set_system_proxy()
        
        if result:
            return {
                'success': True,
                'message': '系统代理设置成功',
                'proxy_address': f"127.0.0.1:{proxy.port}"
            }
        else:
            return {
                'success': False,
                'error': '系统代理设置失败，可能需要管理员权限'
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/system/proxy/unset")
async def unset_system_proxy() -> Dict[str, Any]:
    """手动清除系统代理"""
    try:
        proxy = get_proxy_capture()
        result = proxy.system_proxy_manager.unset_system_proxy()
        
        if result:
            return {
                'success': True,
                'message': '系统代理已清除'
            }
        else:
            return {
                'success': False,
                'error': '系统代理清除失败'
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/auto-start")
async def auto_start_with_setup(auto_setup_proxy: bool = False) -> Dict[str, Any]:  # 默认False
    """
    启动代理服务（可选自动设置系统代理）
    默认不自动设置系统代理以避免权限问题
    """
    if not PROXY_AVAILABLE:
        raise HTTPException(
            status_code=503, 
            detail="代理抓包功能不可用，请安装 mitmproxy 依赖"
        )
    
    try:
        proxy = get_proxy_capture()
        
        # 启动代理，默认不自动设置系统代理
        result = proxy.start(auto_setup_system_proxy=auto_setup_proxy)
        
        if result.get('success'):
            # 根据设置结果返回相应的消息
            if result.get('system_proxy_set', False):
                return {
                    'success': True,
                    'status': 'ready',
                    'message': '代理服务已启动并自动配置系统代理',
                    'details': '现在可以开始浏览网页进行抓包'
                }
            else:
                return {
                    'success': True,
                    'status': 'manual_setup_required',
                    'message': '代理服务已启动',
                    'details': '请手动设置系统代理为 127.0.0.1:8899',
                    'proxy_url': result.get('proxy_url', '127.0.0.1:8899'),
                    'instructions': [
                        '1. 打开系统设置',
                        '2. 进入网络设置',
                        '3. 选择当前网络连接',
                        '4. 点击"高级"或"代理"',
                        '5. 设置HTTP和HTTPS代理为: 127.0.0.1:8899',
                        '6. 保存设置后即可开始抓包'
                    ]
                }
        else:
            raise HTTPException(
                status_code=500, 
                detail=result.get('error', '启动失败')
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"启动代理失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}") 

@router.post("/test/media-detection")
async def test_media_detection():
    """测试媒体资源检测功能"""
    try:
        from ..proxy_capture import get_proxy_capture
        proxy_capture = get_proxy_capture()
        
        # 模拟检测几个媒体资源
        test_result = proxy_capture.simulate_resources()
        
        # 添加当前代理状态
        status = proxy_capture.get_status()
        
        return {
            "success": True,
            "message": "媒体检测测试完成",
            "test_result": test_result,
            "proxy_status": status,
            "debug_info": {
                "mitmproxy_available": proxy_capture.available,
                "port": proxy_capture.port,
                "resources_count": len(proxy_capture.collector.resources)
            }
        }
    except Exception as e:
        logger.error(f"媒体检测测试失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "媒体检测测试失败"
        }

@router.get("/debug/proxy-status")
async def debug_proxy_status():
    """详细的代理状态调试信息"""
    try:
        from ..proxy_capture import get_proxy_capture
        proxy_capture = get_proxy_capture()
        
        # 检查系统代理状态
        system_proxy_result = proxy_capture.check_system_proxy_status()
        
        # 获取当前资源
        resources_result = proxy_capture.get_resources()
        
        # 获取代理状态
        status = proxy_capture.get_status()
        
        return {
            "success": True,
            "proxy_service": status,
            "system_proxy": system_proxy_result,
            "resources": resources_result,
            "debug_info": {
                "platform": platform.system(),
                "python_version": platform.python_version(),
                "mitmproxy_available": MITMPROXY_AVAILABLE if 'MITMPROXY_AVAILABLE' in globals() else False,
            }
        }
    except Exception as e:
        logger.error(f"代理调试失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "代理调试失败"
        }

@router.get("/enhanced-detection", response_model=Dict[str, Any])
async def enhanced_proxy_detection() -> Dict[str, Any]:
    """
    增强的代理检测API - 专门解决与Clash Pro等翻墙工具的冲突
    """
    try:
        manager = EnhancedProxyManager()
        result = manager.comprehensive_proxy_detection()
        
        logging.info(f"增强代理检测结果: {result}")
        return {
            "success": True,
            "detection_result": result,
            "timestamp": asyncio.get_event_loop().time()
        }
        
    except Exception as e:
        logging.error(f"增强代理检测失败: {e}")
        raise HTTPException(status_code=500, detail=f"增强代理检测失败: {str(e)}")

@router.post("/start-smart", response_model=Dict[str, Any])
async def start_smart_proxy(
    force_upstream: bool = Query(False, description="强制使用upstream模式"),
    upstream_proxy: Optional[str] = Query(None, description="指定upstream代理地址")
) -> Dict[str, Any]:
    """
    智能启动代理 - 自动选择最佳模式避免冲突
    """
    try:
        manager = EnhancedProxyManager()
        
        if force_upstream and upstream_proxy:
            # 强制使用指定的upstream代理
            result = proxy.start(auto_setup_system_proxy=False, smart_mode=True)
            if result.get('success'):
                result['message'] = f"强制使用upstream模式: {upstream_proxy}"
                result['upstream_proxy'] = upstream_proxy
            return result
        else:
            # 自动智能检测和启动
            return manager.start_smart_proxy()
            
    except Exception as e:
        logging.error(f"智能代理启动失败: {e}")
        raise HTTPException(status_code=500, detail=f"智能代理启动失败: {str(e)}")

@router.get("/status", summary="获取当前代理状态")
async def get_proxy_status_vs() -> Dict:
    """
    获取当前系统代理设置和状态
    
    Returns:
        Dict: 代理状态信息
    """
    try:
        current_settings = proxy_manager.get_current_proxy_settings()
        detected_proxies = proxy_manager.detect_proxy_software()
        
        return {
            "success": True,
            "data": {
                "current_settings": current_settings,
                "detected_proxies": detected_proxies,
                "platform": proxy_manager.platform
            }
        }
    except Exception as e:
        logger.error(f"获取代理状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取代理状态失败: {str(e)}")

@router.get("/detect", summary="检测代理软件")
async def detect_proxy_software() -> Dict:
    """
    检测运行中的代理软件
    
    Returns:
        Dict: 检测到的代理软件列表
    """
    try:
        detected_proxies = proxy_manager.detect_proxy_software()
        
        return {
            "success": True,
            "data": {
                "detected_proxies": detected_proxies,
                "count": len(detected_proxies)
            }
        }
    except Exception as e:
        logger.error(f"检测代理软件失败: {e}")
        raise HTTPException(status_code=500, detail=f"检测代理软件失败: {str(e)}")

@router.post("/backup", summary="备份当前代理设置")
async def backup_proxy_settings() -> Dict:
    """
    备份当前系统代理设置
    
    Returns:
        Dict: 备份结果
    """
    try:
        success = proxy_manager.backup_current_proxy()
        
        if success:
            return {
                "success": True,
                "message": "代理设置备份成功",
                "data": {
                    "backup_file": str(proxy_manager.backup_file)
                }
            }
        else:
            raise HTTPException(status_code=500, detail="代理设置备份失败")
            
    except Exception as e:
        logger.error(f"备份代理设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"备份代理设置失败: {str(e)}")

@router.post("/set", summary="设置系统代理")
async def set_system_proxy(config: ProxyConfig) -> Dict:
    """
    设置系统代理到VideoSense
    
    Args:
        config: 代理配置
        
    Returns:
        Dict: 设置结果
    """
    try:
        success = proxy_manager.set_system_proxy(
            proxy_host=config.host,
            proxy_port=config.port,
            proxy_type=config.proxy_type
        )
        
        if success:
            return {
                "success": True,
                "message": "系统代理设置成功",
                "data": {
                    "proxy_host": config.host,
                    "proxy_port": config.port,
                    "proxy_type": config.proxy_type
                }
            }
        else:
            raise HTTPException(status_code=500, detail="系统代理设置失败")
            
    except Exception as e:
        logger.error(f"设置系统代理失败: {e}")
        raise HTTPException(status_code=500, detail=f"设置系统代理失败: {str(e)}")

@router.post("/restore", summary="恢复代理设置")
async def restore_proxy_settings() -> Dict:
    """
    恢复之前备份的代理设置
    
    Returns:
        Dict: 恢复结果
    """
    try:
        success = proxy_manager.restore_proxy_settings()
        
        if success:
            return {
                "success": True,
                "message": "代理设置恢复成功"
            }
        else:
            raise HTTPException(status_code=500, detail="代理设置恢复失败")
            
    except Exception as e:
        logger.error(f"恢复代理设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"恢复代理设置失败: {str(e)}")

@router.delete("/clear", summary="清除系统代理")
async def clear_system_proxy() -> Dict:
    """
    清除系统代理设置
    
    Returns:
        Dict: 清除结果
    """
    try:
        success = proxy_manager.clear_system_proxy()
        
        if success:
            return {
                "success": True,
                "message": "系统代理清除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="系统代理清除失败")
            
    except Exception as e:
        logger.error(f"清除系统代理失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除系统代理失败: {str(e)}")

@router.get("/smart-config", summary="获取智能代理配置")
async def get_smart_proxy_config() -> Dict:
    """
    获取智能代理配置建议
    
    Returns:
        Dict: 智能配置建议
    """
    try:
        config = proxy_manager.get_smart_proxy_config()
        
        return {
            "success": True,
            "data": config
        }
    except Exception as e:
        logger.error(f"获取智能代理配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取智能代理配置失败: {str(e)}")

@router.post("/smart-setup", summary="智能代理设置")
async def smart_proxy_setup() -> Dict:
    """
    根据检测结果智能设置代理
    
    Returns:
        Dict: 设置结果
    """
    try:
        # 获取智能配置建议
        config = proxy_manager.get_smart_proxy_config()
        
        recommendation = config.get("recommendation", "direct")
        
        if recommendation == "chain":
            # 代理链模式：设置VideoSense为系统代理，VideoSense会转发到Clash等工具
            success = proxy_manager.set_system_proxy(
                proxy_host="127.0.0.1",
                proxy_port=8899,
                proxy_type="http"
            )
            
            if success:
                return {
                    "success": True,
                    "message": "智能代理链设置成功",
                    "data": {
                        "mode": "proxy_chain",
                        "videosense_proxy": "127.0.0.1:8899",
                        "upstream_proxy": config.get("proxy_chain", []),
                        "recommendation": recommendation
                    }
                }
            else:
                raise HTTPException(status_code=500, detail="智能代理设置失败")
                
        elif recommendation == "direct":
            # 直连模式：设置VideoSense为系统代理
            success = proxy_manager.set_system_proxy(
                proxy_host="127.0.0.1",
                proxy_port=8899,
                proxy_type="http"
            )
            
            if success:
                return {
                    "success": True,
                    "message": "直连模式代理设置成功",
                    "data": {
                        "mode": "direct",
                        "videosense_proxy": "127.0.0.1:8899",
                        "recommendation": recommendation
                    }
                }
            else:
                raise HTTPException(status_code=500, detail="代理设置失败")
        
        else:
            return {
                "success": True,
                "message": "无需设置代理",
                "data": {
                    "mode": "none",
                    "recommendation": recommendation
                }
            }
            
    except Exception as e:
        logger.error(f"智能代理设置失败: {e}")
        raise HTTPException(status_code=500, detail=f"智能代理设置失败: {str(e)}")

@router.get("/conflicts", summary="检测代理冲突")
async def detect_proxy_conflicts() -> Dict:
    """
    检测可能的代理冲突
    
    Returns:
        Dict: 冲突检测结果
    """
    try:
        detected_proxies = proxy_manager.detect_proxy_software()
        current_settings = proxy_manager.get_current_proxy_settings()
        
        conflicts = []
        warnings = []
        
        # 检查是否有多个代理工具同时运行
        if len(detected_proxies) > 1:
            conflicts.append({
                "type": "multiple_proxy_tools",
                "description": "检测到多个代理工具同时运行",
                "tools": [p["name"] for p in detected_proxies]
            })
        
        # 检查系统代理是否已经被其他工具占用
        if current_settings.get("platform") == "macos":
            services = current_settings.get("services", {})
            for service, settings in services.items():
                http_proxy = settings.get("http", {})
                if http_proxy.get("enabled"):
                    port = http_proxy.get("port", "")
                    if port and port != "8899":  # 不是VideoSense的端口
                        conflicts.append({
                            "type": "system_proxy_occupied",
                            "description": f"系统代理已被其他服务占用 ({service})",
                            "details": http_proxy
                        })
        
        # 提供解决建议
        recommendations = []
        if conflicts:
            if any(c["type"] == "multiple_proxy_tools" for c in conflicts):
                recommendations.append("建议使用智能代理链模式，让VideoSense与现有代理工具协同工作")
            if any(c["type"] == "system_proxy_occupied" for c in conflicts):
                recommendations.append("建议先备份当前代理设置，然后使用VideoSense代理")
        
        return {
            "success": True,
            "data": {
                "conflicts_found": len(conflicts) > 0,
                "conflicts": conflicts,
                "warnings": warnings,
                "recommendations": recommendations,
                "detected_proxies": detected_proxies
            }
        }
    except Exception as e:
        logger.error(f"检测代理冲突失败: {e}")
        raise HTTPException(status_code=500, detail=f"检测代理冲突失败: {str(e)}")

@router.get("/health", summary="代理健康检查")
async def proxy_health_check() -> Dict:
    """
    检查代理服务的健康状态
    
    Returns:
        Dict: 健康检查结果
    """
    try:
        import socket
        import requests
        
        health_status = {
            "videosense_proxy_running": False,
            "system_proxy_configured": False,
            "internet_connectivity": False,
            "dns_resolution": False
        }
        
        # 检查VideoSense代理端口是否开放
        try:
            with socket.create_connection(("127.0.0.1", 8899), timeout=3):
                health_status["videosense_proxy_running"] = True
        except:
            pass
        
        # 检查系统代理是否配置为VideoSense
        current_settings = proxy_manager.get_current_proxy_settings()
        if current_settings.get("platform") == "macos":
            services = current_settings.get("services", {})
            for service, settings in services.items():
                http_proxy = settings.get("http", {})
                if http_proxy.get("enabled") and http_proxy.get("port") == "8899":
                    health_status["system_proxy_configured"] = True
                    break
        
        # 检查DNS解析
        try:
            socket.gethostbyname("www.google.com")
            health_status["dns_resolution"] = True
        except:
            pass
        
        # 检查网络连通性（通过代理）
        try:
            if health_status["videosense_proxy_running"]:
                proxies = {
                    "http": "http://127.0.0.1:8899",
                    "https": "http://127.0.0.1:8899"
                }
                response = requests.get("http://httpbin.org/ip", 
                                      proxies=proxies, 
                                      timeout=5)
                if response.status_code == 200:
                    health_status["internet_connectivity"] = True
        except:
            pass
        
        # 计算整体健康分数
        total_checks = len(health_status)
        passed_checks = sum(1 for status in health_status.values() if status)
        health_score = (passed_checks / total_checks) * 100
        
        return {
            "success": True,
            "data": {
                "health_status": health_status,
                "health_score": health_score,
                "overall_status": "healthy" if health_score >= 75 else "issues_detected"
            }
        }
    except Exception as e:
        logger.error(f"代理健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"代理健康检查失败: {str(e)}")

# 兼容旧API端点
@router.get("/enhanced-detection", summary="增强检测（兼容）")
async def enhanced_detection() -> Dict:
    """
    增强检测功能（兼容旧版本API）
    
    Returns:
        Dict: 检测结果
    """
    try:
        # 这是对之前 enhanced_proxy_solution.py 功能的兼容
        config = proxy_manager.get_smart_proxy_config()
        conflicts = await detect_proxy_conflicts()
        
        return {
            "success": True,
            "data": {
                "smart_config": config,
                "conflict_analysis": conflicts["data"]
            }
        }
    except Exception as e:
        logger.error(f"增强检测失败: {e}")
        raise HTTPException(status_code=500, detail=f"增强检测失败: {str(e)}")

@router.post("/start-smart", summary="启动智能代理（兼容）")
async def start_smart_proxy() -> Dict:
    """
    启动智能代理模式（兼容旧版本API）
    
    Returns:
        Dict: 启动结果
    """
    try:
        # 这是对之前 /proxy/start-smart 端点的兼容
        result = await smart_proxy_setup()
        return result
    except Exception as e:
        logger.error(f"启动智能代理失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动智能代理失败: {str(e)}")

# ==================== 修复版本的代理API ====================

@router.post("/start-fixed")
async def start_fixed_proxy(
    auto_setup_proxy: bool = Query(False, description="是否自动设置系统代理"),
    smart_mode: bool = Query(True, description="是否启用智能模式"),
    upstream_proxy: Optional[str] = Query(None, description="上游代理地址")
) -> Dict[str, Any]:
    """
    启动修复版本的代理服务

    这是一个全新实现的代理服务，解决了原版本的线程和事件循环问题
    """
    try:
        logger.info("🚀 启动修复版本的代理服务...")

        # 获取修复版本的代理实例
        fixed_proxy = get_fixed_proxy_instance()

        # 如果启用智能模式，先检测现有代理
        if smart_mode:
            enhanced_manager = EnhancedProxyManager()
            detection_result = enhanced_manager.comprehensive_proxy_detection()

            if detection_result.get('recommendation', {}).get('use_upstream_mode'):
                upstream_proxy = detection_result['recommendation']['upstream_proxy']
                logger.info(f"🔗 智能模式检测到上游代理: {upstream_proxy}")

        # 启动代理服务
        result = fixed_proxy.start(
            auto_setup_system_proxy=auto_setup_proxy,
            smart_mode=smart_mode,
            upstream_proxy=upstream_proxy
        )

        if result['success']:
            logger.info("✅ 修复版本代理服务启动成功")
            return {
                "success": True,
                "message": "修复版本代理服务启动成功",
                "data": {
                    "port": fixed_proxy.port,
                    "upstream_proxy": upstream_proxy,
                    "smart_mode": smart_mode,
                    "version": "fixed"
                }
            }
        else:
            logger.error(f"❌ 修复版本代理服务启动失败: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error', '启动失败'))

    except Exception as e:
        logger.error(f"启动修复版本代理服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")

@router.post("/stop-fixed")
async def stop_fixed_proxy() -> Dict[str, Any]:
    """停止修复版本的代理服务"""
    try:
        logger.info("🛑 停止修复版本的代理服务...")

        fixed_proxy = get_fixed_proxy_instance()
        result = fixed_proxy.stop()

        if result['success']:
            logger.info("✅ 修复版本代理服务已停止")
            return {
                "success": True,
                "message": "修复版本代理服务已停止"
            }
        else:
            logger.error(f"❌ 停止修复版本代理服务失败: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error', '停止失败'))

    except Exception as e:
        logger.error(f"停止修复版本代理服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")

@router.get("/status-fixed")
async def get_fixed_proxy_status() -> Dict[str, Any]:
    """获取修复版本代理服务状态"""
    try:
        fixed_proxy = get_fixed_proxy_instance()
        status = fixed_proxy.get_status()

        return {
            "success": True,
            "data": {
                "running": status['running'],
                "port": status['port'],
                "upstream_proxy": status['upstream_proxy'],
                "resources_count": status['resources_count'],
                "version": "fixed"
            }
        }

    except Exception as e:
        logger.error(f"获取修复版本代理状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/resources-fixed")
async def get_fixed_proxy_resources() -> Dict[str, Any]:
    """获取修复版本代理捕获的媒体资源"""
    try:
        fixed_proxy = get_fixed_proxy_instance()
        resources = fixed_proxy.get_resources()

        return {
            "success": True,
            "data": {
                "resources": resources,
                "count": len(resources),
                "version": "fixed"
            }
        }

    except Exception as e:
        logger.error(f"获取修复版本代理资源失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取资源失败: {str(e)}")

@router.post("/clear-resources-fixed")
async def clear_fixed_proxy_resources() -> Dict[str, Any]:
    """清空修复版本代理的媒体资源列表"""
    try:
        fixed_proxy = get_fixed_proxy_instance()
        fixed_proxy.clear_resources()

        return {
            "success": True,
            "message": "资源列表已清空"
        }

    except Exception as e:
        logger.error(f"清空修复版本代理资源失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空资源失败: {str(e)}")

@router.get("/test-fixed")
async def test_fixed_proxy() -> Dict[str, Any]:
    """测试修复版本的代理功能"""
    try:
        logger.info("🧪 测试修复版本的代理功能...")

        fixed_proxy = get_fixed_proxy_instance()

        # 测试启动
        start_result = fixed_proxy.start(smart_mode=True)

        if start_result['success']:
            # 等待一下
            import time
            time.sleep(2)

            # 检查状态
            status = fixed_proxy.get_status()

            # 停止服务
            stop_result = fixed_proxy.stop()

            return {
                "success": True,
                "message": "修复版本代理测试完成",
                "data": {
                    "start_result": start_result,
                    "status": status,
                    "stop_result": stop_result
                }
            }
        else:
            return {
                "success": False,
                "message": "修复版本代理测试失败",
                "error": start_result.get('error')
            }

    except Exception as e:
        logger.error(f"测试修复版本代理失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")

# ==================== 简单版本的代理API ====================

@router.post("/start-simple")
async def start_simple_proxy(
    auto_setup_proxy: bool = Query(True, description="是否自动设置系统代理"),
    smart_mode: bool = Query(True, description="是否启用智能模式"),
    upstream_proxy: Optional[str] = Query(None, description="上游代理地址")
) -> Dict[str, Any]:
    """
    启动简单版本的代理服务

    这是一个基于原生socket的简单HTTP代理实现，避免了mitmproxy的复杂性
    """
    try:
        logger.info("🚀 启动简单版本的代理服务...")

        # 获取简单版本的代理实例
        simple_proxy = get_simple_proxy_instance()

        # 如果启用智能模式，先检测现有代理
        if smart_mode:
            enhanced_manager = EnhancedProxyManager()
            detection_result = enhanced_manager.comprehensive_proxy_detection()

            if detection_result.get('recommendation', {}).get('use_upstream_mode'):
                upstream_proxy = detection_result['recommendation']['upstream_proxy']
                logger.info(f"🔗 智能模式检测到上游代理: {upstream_proxy}")

        # 设置上游代理
        if upstream_proxy:
            simple_proxy.upstream_proxy = upstream_proxy

        # 启动代理服务
        result = simple_proxy.start(auto_set_system_proxy=auto_setup_proxy)

        if result['success']:
            logger.info("✅ 简单版本代理服务启动成功")
            return {
                "success": True,
                "message": result['message'],
                "data": {
                    "port": simple_proxy.port,
                    "upstream_proxy": upstream_proxy,
                    "smart_mode": smart_mode,
                    "version": "simple",
                    "system_proxy_enabled": result.get('system_proxy_enabled', False),
                    "usage_tip": result.get('usage_tip', '')
                }
            }
        else:
            logger.error(f"❌ 简单版本代理服务启动失败: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error', '启动失败'))

    except Exception as e:
        logger.error(f"启动简单版本代理服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")

@router.post("/stop-simple")
async def stop_simple_proxy() -> Dict[str, Any]:
    """停止简单版本的代理服务"""
    try:
        logger.info("🛑 停止简单版本的代理服务...")

        simple_proxy = get_simple_proxy_instance()
        result = simple_proxy.stop()

        if result['success']:
            logger.info("✅ 简单版本代理服务已停止")
            return {
                "success": True,
                "message": "简单版本代理服务已停止"
            }
        else:
            logger.error(f"❌ 停止简单版本代理服务失败: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get('error', '停止失败'))

    except Exception as e:
        logger.error(f"停止简单版本代理服务失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")

@router.get("/status-simple")
async def get_simple_proxy_status() -> Dict[str, Any]:
    """获取简单版本代理服务状态"""
    try:
        simple_proxy = get_simple_proxy_instance()
        status = simple_proxy.get_status()

        return {
            "success": True,
            "data": {
                "running": status['running'],
                "port": status['port'],
                "upstream_proxy": status['upstream_proxy'],
                "resources_count": status['resources_count'],
                "version": "simple"
            }
        }

    except Exception as e:
        logger.error(f"获取简单版本代理状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/resources-simple")
async def get_simple_proxy_resources() -> Dict[str, Any]:
    """获取简单版本代理捕获的媒体资源"""
    try:
        simple_proxy = get_simple_proxy_instance()
        resources = simple_proxy.get_resources()

        return {
            "success": True,
            "data": {
                "resources": resources,
                "count": len(resources),
                "version": "simple"
            }
        }

    except Exception as e:
        logger.error(f"获取简单版本代理资源失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取资源失败: {str(e)}")

@router.post("/clear-resources-simple")
async def clear_simple_proxy_resources() -> Dict[str, Any]:
    """清空简单版本代理的媒体资源列表"""
    try:
        simple_proxy = get_simple_proxy_instance()
        simple_proxy.clear_resources()

        return {
            "success": True,
            "message": "资源列表已清空"
        }

    except Exception as e:
        logger.error(f"清空简单版本代理资源失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空资源失败: {str(e)}")

@router.get("/test-simple")
async def test_simple_proxy() -> Dict[str, Any]:
    """测试简单版本的代理功能"""
    try:
        logger.info("🧪 测试简单版本的代理功能...")

        simple_proxy = get_simple_proxy_instance()

        # 测试启动
        start_result = simple_proxy.start()

        if start_result['success']:
            # 等待一下
            import time
            time.sleep(2)

            # 检查状态
            status = simple_proxy.get_status()

            # 停止服务
            stop_result = simple_proxy.stop()

            return {
                "success": True,
                "message": "简单版本代理测试完成",
                "data": {
                    "start_result": start_result,
                    "status": status,
                    "stop_result": stop_result
                }
            }
        else:
            return {
                "success": False,
                "message": "简单版本代理测试失败",
                "error": start_result.get('error')
            }

    except Exception as e:
        logger.error(f"测试简单版本代理失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")

#!/bin/bash

# VideoSense 修复版本代理测试脚本
# 专门测试新的修复版本代理抓包功能

echo "🔧 VideoSense 修复版本代理测试"
echo "================================"

# 检查后端服务是否运行
check_backend() {
    echo "🔍 检查后端服务状态..."
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 后端服务正在运行"
        return 0
    else
        echo "❌ 后端服务未运行，请先启动后端服务"
        return 1
    fi
}

# 测试修复版本代理API
test_fixed_proxy_api() {
    echo ""
    echo "🧪 测试修复版本代理API..."
    
    # 1. 测试代理状态
    echo "📋 测试1: 获取代理状态"
    STATUS_RESULT=$(curl -s http://localhost:8000/proxy/status-fixed)
    echo "状态结果: $STATUS_RESULT"
    
    # 2. 测试启动代理（智能模式）
    echo ""
    echo "📋 测试2: 启动修复版本代理（智能模式）"
    START_RESULT=$(curl -s -X POST "http://localhost:8000/proxy/start-fixed?smart_mode=true")
    echo "启动结果: $START_RESULT"
    
    # 检查启动是否成功
    if echo "$START_RESULT" | grep -q '"success":true'; then
        echo "✅ 修复版本代理启动成功"
        
        # 等待一下让代理稳定
        echo "⏳ 等待代理服务稳定..."
        sleep 3
        
        # 3. 再次检查状态
        echo ""
        echo "📋 测试3: 检查运行状态"
        RUNNING_STATUS=$(curl -s http://localhost:8000/proxy/status-fixed)
        echo "运行状态: $RUNNING_STATUS"
        
        # 4. 测试资源获取
        echo ""
        echo "📋 测试4: 获取捕获的资源"
        RESOURCES_RESULT=$(curl -s http://localhost:8000/proxy/resources-fixed)
        echo "资源结果: $RESOURCES_RESULT"
        
        # 5. 测试端口连通性
        echo ""
        echo "📋 测试5: 检查代理端口连通性"
        if nc -z 127.0.0.1 8899 2>/dev/null; then
            echo "✅ 代理端口8899可连接"
        else
            echo "❌ 代理端口8899不可连接"
        fi
        
        # 6. 停止代理
        echo ""
        echo "📋 测试6: 停止修复版本代理"
        STOP_RESULT=$(curl -s -X POST http://localhost:8000/proxy/stop-fixed)
        echo "停止结果: $STOP_RESULT"
        
        if echo "$STOP_RESULT" | grep -q '"success":true'; then
            echo "✅ 修复版本代理停止成功"
        else
            echo "❌ 修复版本代理停止失败"
        fi
        
    else
        echo "❌ 修复版本代理启动失败"
        echo "错误信息: $START_RESULT"
    fi
}

# 测试完整的代理功能
test_complete_proxy_function() {
    echo ""
    echo "🎯 测试完整的代理抓包功能..."
    
    # 启动代理
    echo "🚀 启动修复版本代理..."
    START_RESULT=$(curl -s -X POST "http://localhost:8000/proxy/start-fixed?smart_mode=true")
    
    if echo "$START_RESULT" | grep -q '"success":true'; then
        echo "✅ 代理启动成功"
        
        # 等待稳定
        sleep 2
        
        # 模拟一些HTTP请求来测试抓包
        echo "📡 模拟HTTP请求测试抓包功能..."
        
        # 设置代理并发送测试请求
        export http_proxy=http://127.0.0.1:8899
        export https_proxy=http://127.0.0.1:8899
        
        # 发送一些测试请求
        echo "发送测试请求..."
        curl -s --proxy http://127.0.0.1:8899 http://httpbin.org/get > /dev/null 2>&1 || echo "请求1完成"
        curl -s --proxy http://127.0.0.1:8899 http://httpbin.org/user-agent > /dev/null 2>&1 || echo "请求2完成"
        
        # 清除代理设置
        unset http_proxy
        unset https_proxy
        
        # 等待处理
        sleep 2
        
        # 检查捕获的资源
        echo "🔍 检查捕获的资源..."
        RESOURCES=$(curl -s http://localhost:8000/proxy/resources-fixed)
        echo "捕获的资源: $RESOURCES"
        
        # 停止代理
        echo "🛑 停止代理..."
        curl -s -X POST http://localhost:8000/proxy/stop-fixed > /dev/null
        
        echo "✅ 完整功能测试完成"
    else
        echo "❌ 代理启动失败，无法进行完整测试"
    fi
}

# 对比测试原版本和修复版本
compare_versions() {
    echo ""
    echo "⚖️ 对比原版本和修复版本..."
    
    echo "📊 原版本代理状态:"
    ORIGINAL_STATUS=$(curl -s http://localhost:8000/proxy/status 2>/dev/null || echo "获取失败")
    echo "$ORIGINAL_STATUS"
    
    echo ""
    echo "📊 修复版本代理状态:"
    FIXED_STATUS=$(curl -s http://localhost:8000/proxy/status-fixed 2>/dev/null || echo "获取失败")
    echo "$FIXED_STATUS"
}

# 主测试流程
main() {
    echo "🎯 开始VideoSense修复版本代理测试"
    echo ""
    
    # 检查后端服务
    if ! check_backend; then
        echo "❌ 请先启动后端服务: cd Project/backend && python main.py"
        exit 1
    fi
    
    # 测试修复版本API
    test_fixed_proxy_api
    
    # 测试完整功能
    test_complete_proxy_function
    
    # 对比版本
    compare_versions
    
    echo ""
    echo "🎉 修复版本代理测试完成!"
    echo ""
    echo "📋 测试总结:"
    echo "  • 修复版本API: 已测试"
    echo "  • 代理启动/停止: 已测试"
    echo "  • 端口连通性: 已测试"
    echo "  • 抓包功能: 已测试"
    echo ""
    echo "💡 使用说明:"
    echo "  1. 修复版本解决了原版本的线程问题"
    echo "  2. 使用新的API端点: /proxy/*-fixed"
    echo "  3. 支持智能模式和上游代理"
    echo "  4. 更稳定的事件循环管理"
}

# 执行主流程
main

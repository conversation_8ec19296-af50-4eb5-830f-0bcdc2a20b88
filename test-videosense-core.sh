#!/bin/bash

# VideoSense 核心功能测试脚本
# 专门用于测试后端API和Electron客户端

echo "🧪 VideoSense 核心功能测试"
echo "=========================="

# 检查系统平台
PLATFORM=$(uname)
echo "📋 检测到系统: $PLATFORM"

# 检查 Python
if ! command -v python &> /dev/null; then
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 未安装，请先安装 Python"
        exit 1
    else
        PYTHON=python3
    fi
else
    PYTHON=python
fi

echo "✅ Python 版本: $($PYTHON --version)"

# 函数：清理端口
cleanup_ports() {
    echo "🧹 清理端口冲突..."
    
    # 清理8000端口
    if lsof -ti:8000 > /dev/null 2>&1; then
        echo "🔄 发现端口8000被占用，正在清理..."
        lsof -ti:8000 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # 清理8899端口
    if lsof -ti:8899 > /dev/null 2>&1; then
        echo "🔄 发现端口8899被占用，正在清理..."
        lsof -ti:8899 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    echo "✅ 端口清理完成"
}

# 函数：测试后端API
test_backend() {
    echo "🔧 测试后端API..."
    cd Project/backend
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        echo "📥 创建虚拟环境..."
        $PYTHON -m venv venv
    fi
    
    # 激活虚拟环境
    if [ "$PLATFORM" = "Windows_NT" ]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    # 安装依赖（修复版本冲突）
    echo "📥 安装Python依赖（修复版本）..."
    pip install -r requirements.txt
    
    # 启动后端（测试模式）
    echo "🔄 启动后端服务（测试模式）..."
    $PYTHON main.py &
    BACKEND_PID=$!
    
    cd ../..
    
    # 等待后端启动
    echo "⏳ 等待后端服务启动..."
    sleep 5
    
    # 测试健康检查
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 后端API健康检查通过"
        
        # 测试其他API端点
        echo "🔍 测试API端点..."
        
        # 测试代理状态
        if curl -s http://localhost:8000/proxy/status > /dev/null 2>&1; then
            echo "✅ 代理状态API正常"
        else
            echo "⚠️ 代理状态API异常"
        fi
        
        # 测试系统检测
        if curl -s http://localhost:8000/proxy/system/detect-existing-proxy > /dev/null 2>&1; then
            echo "✅ 系统检测API正常"
        else
            echo "⚠️ 系统检测API异常"
        fi
        
        return 0
    else
        echo "❌ 后端服务启动失败"
        return 1
    fi
}

# 函数：测试Electron客户端
test_electron() {
    echo "⚡ 测试Electron客户端..."
    cd Project/electron
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo "📥 安装Electron依赖..."
        npm install
    fi
    
    # 启动Electron（测试模式）
    echo "🔄 启动Electron应用（测试模式）..."
    npm start &
    ELECTRON_PID=$!
    
    cd ../..
    
    # 等待Electron启动
    echo "⏳ 等待Electron应用启动..."
    sleep 8
    
    echo "✅ Electron客户端已启动"
    return 0
}

# 函数：运行功能测试
run_tests() {
    echo "🧪 运行功能测试..."
    
    # 测试1: 后端健康检查
    echo "📋 测试1: 后端健康检查"
    if curl -s http://localhost:8000/health | grep -q "ok\|healthy\|success"; then
        echo "✅ 测试1通过: 后端服务正常"
    else
        echo "❌ 测试1失败: 后端服务异常"
    fi
    
    # 测试2: 代理检测功能
    echo "📋 测试2: 代理检测功能"
    PROXY_RESULT=$(curl -s http://localhost:8000/proxy/system/detect-existing-proxy)
    if [ ! -z "$PROXY_RESULT" ]; then
        echo "✅ 测试2通过: 代理检测功能正常"
        echo "   检测结果: $PROXY_RESULT"
    else
        echo "❌ 测试2失败: 代理检测功能异常"
    fi
    
    # 测试3: 前端静态文件
    echo "📋 测试3: 前端静态文件"
    if [ -f "Project/frontend/out/index.html" ]; then
        echo "✅ 测试3通过: 前端静态文件存在"
    else
        echo "❌ 测试3失败: 前端静态文件缺失"
    fi
}

# 函数：清理进程
cleanup() {
    echo ""
    echo "🧹 清理测试进程..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo "🔄 后端服务已停止"
    fi
    
    if [ ! -z "$ELECTRON_PID" ]; then
        kill $ELECTRON_PID 2>/dev/null || true
        echo "🔄 Electron应用已停止"
    fi
    
    echo "✅ 清理完成"
    exit 0
}

# 捕获退出信号
trap cleanup SIGINT SIGTERM

# 主测试流程
main() {
    echo ""
    echo "🎯 VideoSense 核心功能测试开始"
    echo ""
    
    # 清理端口
    cleanup_ports
    
    # 测试后端
    if test_backend; then
        echo ""
        # 测试Electron
        test_electron
        echo ""
        
        # 运行功能测试
        run_tests
        echo ""
        
        echo "🎉 核心功能测试完成!"
        echo ""
        echo "📋 测试结果总结:"
        echo "  • 后端服务: ✅ 正常运行"
        echo "  • Electron客户端: ✅ 正常启动"
        echo "  • API端点: ✅ 响应正常"
        echo ""
        echo "💡 测试说明:"
        echo "  1. 后端服务运行在 http://localhost:8000"
        echo "  2. Electron客户端已启动，可以进行手动测试"
        echo "  3. 按 Ctrl+C 可安全停止所有服务"
        echo ""
        
        # 等待用户输入或信号
        wait
    else
        echo "❌ 后端服务测试失败，无法继续"
        exit 1
    fi
}

# 执行主流程
main
